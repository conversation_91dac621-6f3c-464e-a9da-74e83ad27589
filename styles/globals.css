/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Custom Properties - Teal Professional Theme */
:root {
    /* Teal Professional Color Palette */
    --primary-color: #00C2B3;
    --primary-hover: #00A89B;
    --secondary-color: #007B83;
    --accent-color: #00A59C;
    --text-primary: #1E1E1E;
    --text-secondary: #5A5A5A;
    --text-light: #9E9E9E;
    --background: #FCFCFD;
    --background-light: #FFFFFF;
    --surface: #FFFFFF;
    --border-color: #E0E0E0;
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.15), 0 4px 6px -4px rgba(0, 0, 0, 0.05);
    --gradient-primary: linear-gradient(135deg, #00C2B3 0%, #00A89B 100%);
    --gradient-secondary: linear-gradient(135deg, #FAFBFC 0%, #FFFFFF 100%);
    --gradient-background: linear-gradient(135deg, #FAFBFC 0%, #FFFFFF 100%);

    /* Special Colors */
    --star-rating: #FFC107;
    --availability: #38B000;
    --verified: #38B000;
    --accent-text: #00A59C;
    --success-light: #C7F4E5;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    overflow-x: hidden;
    min-height: 100vh;
    width: 100%;
    max-width: 100vw;
}

/* Container System */
.responsive-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    box-sizing: border-box;
}

/* Global container fixes for mobile */
.container, .main-container, .page-container {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: hidden;
}

@media (min-width: 768px) {
    .responsive-container {
        padding: 0 1.5rem;
    }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    padding: 0.75rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.5rem;
    text-decoration: none;
    transition: color 0.2s ease;
}

.nav-logo:hover {
    color: var(--primary-hover);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link.active {
    color: var(--primary-color);
    font-weight: 600;
}

/* Profile Dropdown */
.profile-dropdown {
    position: relative;
}

.profile-trigger {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.profile-trigger:hover {
    background-color: var(--background);
}

.profile-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.profile-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.profile-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: var(--text-primary);
    transition: background-color 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.profile-menu-item:hover {
    background-color: var(--background);
}

.profile-menu-item:first-child {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.profile-menu-item:last-child {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.profile-menu-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 0.5rem 0;
}

/* Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 44px;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    position: relative;
    overflow: hidden;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: 1px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--primary-hover) !important;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

.btn-secondary {
    background: var(--surface);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    background: var(--background);
    border-color: var(--primary-color);
}

.btn-full-mobile {
    width: 100%;
}

@media (min-width: 768px) {
    .btn-full-mobile {
        width: auto;
    }
}

/* Google Login Button */
.google-login-btn {
    background: white;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

.google-login-btn:hover {
    background: var(--background);
    border-color: var(--primary-color);
}

.google-icon-wrapper {
    display: flex;
    align-items: center;
    margin-right: 0.5rem;
}

/* Hero Section */
.hero {
    padding: 6rem 0 4rem;
    background: var(--gradient-background);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content {
    text-align: left;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-features {
    list-style: none;
    margin-bottom: 3rem;
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.hero-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    color: var(--text-secondary);
}

.feature-icon {
    font-size: 1.5rem;
}

.cta-button {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius-lg);
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
}

.cta-button:hover {
    background: var(--primary-hover);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.google-icon {
    display: flex;
    align-items: center;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-illustration {
    width: 100%;
    max-width: 500px;
    height: 400px;
    background: var(--gradient-secondary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}



.btn-full {
    width: 100%;
}

/* Hero Section */
.hero {
    padding: 8rem 0 4rem;
    background: var(--gradient-background);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    text-align: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-features {
    list-style: none;
    margin-bottom: 3rem;
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.hero-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    color: var(--text-secondary);
}

.feature-icon {
    font-size: 1.5rem;
}

.cta-button {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius-lg);
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
    text-decoration: none;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.google-icon {
    background: white;
    border-radius: 6px;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Languages Section */
.languages {
    padding: 4rem 0;
    background: white;
}

.languages-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    text-align: center;
}

.languages-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: var(--text-primary);
}

.languages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.language-pill {
    background: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem 1rem;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.language-pill:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.language-name {
    display: block;
    font-weight: 600;
    font-size: 1.125rem;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.tutor-count {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* CTA Section */
.cta {
    padding: 4rem 0;
    background: var(--gradient-background);
}

.cta-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    text-align: center;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.cta-description {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Marketplace Styles */
.tutor-marketplace {
    max-width: 1200px;
    margin: 0 auto;
    padding: 80px 1rem 2rem 1rem;
    background: var(--background);
    min-height: 100vh;
}

.marketplace-header {
    margin-bottom: 2rem;
    text-align: center;
}

.marketplace-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.marketplace-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
}

.tutor-count {
    color: var(--primary-color);
    font-weight: 600;
}

/* Search Filters */
.search-filters {
    background: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filters-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-end;
}

.filter-group, .search-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.search-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label, .search-group label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.filter-select, .search-input {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.filter-select:focus, .search-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Tutor Cards */
.tutors-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.tutor-card-container {
    display: flex;
    align-items: stretch;
    gap: 20px;
    position: relative;
    width: 100%;
}

.tutor-card {
    background: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    width: 100%;
}

.tutor-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.tutor-content {
    display: flex;
    padding: 1.5rem;
    gap: 1.5rem;
}

.tutor-left-section {
    flex: 1;
}

.tutor-header {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.tutor-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.tutor-info h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.tutor-language {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.tutor-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.tutor-bio {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.5;
}

.tutor-right-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: space-between;
    min-width: 150px;
}

.tutor-pricing {
    text-align: right;
    margin-bottom: 1rem;
}

.price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.price-unit {
    display: block;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.tutor-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
}

/* No Results */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

/* Footer */
.site-footer {
    background: var(--text-primary);
    color: #ffffff;
    margin-top: 4rem;
    position: relative;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    clear: both;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    width: 100%;
    box-sizing: border-box;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    padding: 3rem 0 2rem;
}

.footer-section h3, .footer-section h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid #333;
    padding: 1rem 0;
    text-align: center;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-features {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .languages-title, .cta-title {
        font-size: 2rem;
    }
    
    .filters-container {
        flex-direction: column;
    }
    
    .filter-group, .search-group {
        min-width: 100%;
    }
    
    .tutor-content {
        flex-direction: column;
    }
    
    .tutor-right-section {
        align-items: flex-start;
        min-width: auto;
    }
    
    .tutor-actions {
        flex-direction: row;
    }
    
    .nav-links {
        display: none;
    }
}

/* Modal Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-fadeIn {
    animation: fadeIn 0.2s ease-out;
}

/* Dashboard Styles */
.dashboard-page {
    min-height: 100vh;
    background: var(--background);
    padding-top: 80px;
}

.dashboard-main {
    padding: 2rem 0;
}

.welcome-section {
    margin-bottom: 2rem;
    text-align: center;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.welcome-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.dashboard-card {
    background: var(--surface);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.view-all-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-weight: 500;
    cursor: pointer;
    transition: color 0.2s ease;
}

.view-all-btn:hover {
    color: var(--primary-hover);
}

.progress-content {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.progress-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.progress-info {
    flex: 1;
}

.progress-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.progress-stats {
    display: flex;
    gap: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.lessons-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.lesson-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--background);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.lesson-info {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.lesson-date {
    text-align: center;
    min-width: 60px;
}

.date-text {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.time-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.lesson-details {
    flex: 1;
}

.lesson-language {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.lesson-duration {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.lesson-join-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.no-lessons {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
}

.no-lessons-text {
    margin-bottom: 1rem;
}

.book-lesson-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.book-lesson-btn:hover {
    background: var(--primary-hover);
}

.teachers-section {
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
}

.teachers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.placeholder-card {
    opacity: 0.7;
    border-style: dashed;
}

.placeholder-actions {
    text-align: center;
    padding: 1rem 0;
}

.find-tutors-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.find-tutors-btn:hover {
    background: var(--primary-hover);
}

.tutor-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.tutor-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.tutor-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.star-icon {
    color: var(--star-rating);
}

.rating-value {
    font-weight: 500;
    color: var(--text-primary);
}

.tutor-rate {
    font-weight: 600;
    color: var(--primary-color);
}

.tutor-view-btn, .tutor-contact-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    flex: 1;
}

.tutor-view-btn {
    margin-right: 0.5rem;
}

/* Loading Screen */
.loading-screen {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background);
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-secondary);
    font-size: 1rem;
}
