// Landing Page with SSG for SEO
import { GetStaticProps } from 'next'
import Head from 'next/head'
import Link from 'next/link'
import { useState, useEffect } from 'react'
import { generateMetadata, generateMarketplaceStructuredData } from '../lib/seo'
import { db } from '../lib/supabase'
import LoginModal from '../components/LoginModal'

interface LandingPageProps {
  tutors: any[]
  tutorCount: number
}

export default function LandingPage({ tutors, tutorCount }: LandingPageProps) {
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // Debug logging
  useEffect(() => {
    console.log('🔥 LandingPage component mounted!')
    console.log('🔥 showLoginModal state:', showLoginModal)
  }, [])

  useEffect(() => {
    console.log('🔥 showLoginModal state changed to:', showLoginModal)
    if (showLoginModal) {
      console.log('🔥 Modal should be visible now!')
    }
  }, [showLoginModal])

  const showSuccessMessage = (text: string) => {
    setMessage({ type: 'success', text })
    setTimeout(() => setMessage(null), 5000)
  }

  const showErrorMessage = (text: string) => {
    setMessage({ type: 'error', text })
    setTimeout(() => setMessage(null), 5000)
  }

  const metadata = generateMetadata({
    title: 'Learn Indian Languages with Expert Tutors',
    description: `Connect with ${tutorCount}+ qualified Indian language tutors. Learn Hindi, Tamil, Bengali, Telugu and more with certified teachers. 1-on-1 video lessons, flexible schedules.`,
    keywords: 'Indian language tutors, Hindi tutor, Tamil tutor, Bengali tutor, online language learning, certified teachers',
    path: '/'
  })

  const structuredData = generateMarketplaceStructuredData(tutors.slice(0, 10))

  return (
    <>
      <Head>
        <title>{metadata.title as string}</title>
        <meta name="description" content={metadata.description} />
        <meta name="keywords" content={Array.isArray(metadata.keywords) ? metadata.keywords.join(', ') : metadata.keywords} />
        
        {/* Open Graph */}
        <meta property="og:title" content={metadata.title as string} />
        <meta property="og:description" content={metadata.description} />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://www.shyamsyangtan.com/" />
        <meta property="og:image" content="https://www.shyamsyangtan.com/og-image.jpg" />
        
        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={metadata.title as string} />
        <meta name="twitter:description" content={metadata.description} />
        <meta name="twitter:image" content="https://www.shyamsyangtan.com/og-image.jpg" />
        
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
        
        {/* Canonical URL */}
        <link rel="canonical" href="https://www.shyamsyangtan.com/" />
      </Head>

      <div className="landing-page">
        {/* Navigation */}
        <nav className="navbar">
          <div className="nav-container">
            <Link href="/" className="nav-logo">
              IndianTutors
            </Link>
            <div className="nav-links">
              <Link href="/marketplace" className="nav-link">Find a Teacher</Link>
              <button
                className="btn btn-primary"
                onClick={() => {
                  console.log('🔥 LOGIN BUTTON CLICKED!')
                  console.log('🔥 Current state:', showLoginModal)
                  setShowLoginModal(true)
                  console.log('🔥 State should now be true')
                }}
              >
                Log in
              </button>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="hero">
          <div className="hero-container">
            <h1 className="hero-title">Become fluent in any Indian language</h1>
            <ul className="hero-features">
              <li>
                <span className="feature-icon">📹</span>
                1-on-1 video lessons
              </li>
              <li>
                <span className="feature-icon">🎓</span>
                {tutorCount}+ certified tutors
              </li>
              <li>
                <span className="feature-icon">⏰</span>
                Flexible schedules and prices
              </li>
            </ul>
            <button
              className="btn btn-primary cta-button"
              onClick={() => {
                console.log('🔥 CTA BUTTON CLICKED!')
                console.log('🔥 Current state:', showLoginModal)
                setShowLoginModal(true)
                console.log('🔥 State should now be true')
              }}
            >
              <span className="google-icon">
                <svg width="20" height="20" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
              </span>
              Continue with Google
            </button>
          </div>
        </section>

        {/* Languages Section */}
        <section className="languages">
          <div className="languages-container">
            <h2 className="languages-title">Popular Indian Languages</h2>
            <div className="languages-grid">
              <div className="language-pill">
                <span className="language-name">Hindi</span>
                <span className="tutor-count">1,247 tutors</span>
              </div>
              <div className="language-pill">
                <span className="language-name">Tamil</span>
                <span className="tutor-count">892 tutors</span>
              </div>
              <div className="language-pill">
                <span className="language-name">Bengali</span>
                <span className="tutor-count">634 tutors</span>
              </div>
              <div className="language-pill">
                <span className="language-name">Telugu</span>
                <span className="tutor-count">567 tutors</span>
              </div>
              <div className="language-pill">
                <span className="language-name">Marathi</span>
                <span className="tutor-count">423 tutors</span>
              </div>
              <div className="language-pill">
                <span className="language-name">Gujarati</span>
                <span className="tutor-count">389 tutors</span>
              </div>
              <div className="language-pill">
                <span className="language-name">Punjabi</span>
                <span className="tutor-count">312 tutors</span>
              </div>
              <div className="language-pill">
                <span className="language-name">Kannada</span>
                <span className="tutor-count">298 tutors</span>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="cta">
          <div className="cta-container">
            <h2 className="cta-title">Start learning today</h2>
            <p className="cta-description">
              Join thousands of students learning Indian languages with our certified tutors
            </p>
            <Link href="/marketplace" className="btn btn-primary cta-button">
              Browse Tutors
            </Link>
          </div>
        </section>

        {/* Footer */}
        <footer className="site-footer">
          <div className="footer-container">
            <div className="footer-content">
              <div className="footer-section">
                <h3>IndianTutors</h3>
                <p>Connect with qualified tutors for personalized learning experiences.</p>
              </div>
              <div className="footer-section">
                <h4>Company</h4>
                <ul>
                  <li><Link href="/about">About Us</Link></li>
                  <li><Link href="/contact">Contact Us</Link></li>
                  <li><Link href="/careers">Careers</Link></li>
                </ul>
              </div>
              <div className="footer-section">
                <h4>Legal</h4>
                <ul>
                  <li><Link href="/privacy">Privacy Policy</Link></li>
                  <li><Link href="/terms">Terms of Service</Link></li>
                  <li><Link href="/cookies">Cookie Policy</Link></li>
                </ul>
              </div>
              <div className="footer-section">
                <h4>Support</h4>
                <ul>
                  <li><Link href="/help">Help Center</Link></li>
                  <li><Link href="/community">Community Guidelines</Link></li>
                  <li><Link href="/safety">Safety</Link></li>
                </ul>
              </div>
            </div>
            <div className="footer-bottom">
              <p>&copy; 2025 IndianTutors. All rights reserved.</p>
            </div>
          </div>
        </footer>

        {/* Login Modal */}
        {console.log('🔥 RENDER: About to render with showLoginModal =', showLoginModal)}

        {/* Test Modal - Simple version with more visibility */}
        {showLoginModal ? (
          <div
            key="test-modal"
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(255, 0, 0, 0.8)', // Red background to make it super obvious
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 99999,
              fontSize: '24px',
              color: 'white',
              fontWeight: 'bold'
            }}
            onClick={() => {
              console.log('🔥 Clicking overlay to close')
              setShowLoginModal(false)
            }}
          >
            <div
              style={{
                backgroundColor: 'white',
                color: 'black',
                borderRadius: '8px',
                maxWidth: '500px',
                width: '90%',
                margin: '0 16px',
                padding: '40px',
                textAlign: 'center',
                border: '5px solid red'
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <h2 style={{ marginBottom: '20px', fontSize: '28px', fontWeight: 'bold', color: 'red' }}>
                🎉 TEST MODAL IS WORKING! 🎉
              </h2>
              <p style={{ marginBottom: '20px', fontSize: '18px' }}>
                State management is working correctly!<br/>
                showLoginModal = {String(showLoginModal)}
              </p>
              <button
                onClick={() => {
                  console.log('🔥 Closing test modal via button')
                  setShowLoginModal(false)
                }}
                style={{
                  backgroundColor: '#00C2B3',
                  color: 'white',
                  padding: '12px 24px',
                  borderRadius: '6px',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '16px',
                  fontWeight: 'bold'
                }}
              >
                Close Test Modal
              </button>
            </div>
          </div>
        ) : (
          <div key="no-modal" style={{ display: 'none' }}>
            {console.log('🔥 RENDER: Modal should NOT be visible, showLoginModal =', showLoginModal)}
          </div>
        )}

        <LoginModal
          isOpen={showLoginModal}
          onClose={() => {
            console.log('🔥 Closing modal')
            setShowLoginModal(false)
          }}
          showSuccessMessage={showSuccessMessage}
          showErrorMessage={showErrorMessage}
        />

        {/* Message Display */}
        {message && (
          <div className={`fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg ${
            message.type === 'success'
              ? 'bg-green-100 border border-green-400 text-green-700'
              : 'bg-red-100 border border-red-400 text-red-700'
          }`}>
            {message.text}
          </div>
        )}
      </div>
    </>
  )
}

// Static Site Generation for SEO
export const getStaticProps: GetStaticProps = async () => {
  try {
    // Fetch tutors data for SEO
    const { data: tutors } = await db.getTutors()
    
    return {
      props: {
        tutors: tutors || [],
        tutorCount: tutors?.length || 0
      }
    }
  } catch (error) {
    console.error('Error fetching tutors for landing page:', error)
    return {
      props: {
        tutors: [],
        tutorCount: 0
      }
    }
  }
}
